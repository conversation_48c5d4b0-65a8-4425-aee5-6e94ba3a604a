'use client';

import { useState, useCallback } from 'react';
import { signIn } from 'next-auth/react';
import { useAuthState } from '@/app/contexts/AuthStateContext';
import EmailIcon from '@/app/components/icons/EmailIcon';

export default function EmailSignIn() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  const {
    isAnyAuthLoading,
    showLoading,
    setAuthLoading,
    setAuthCompleted,
    resetAuthState,
    activeProvider,
    isAuthCompleted
  } = useAuthState();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    if (isAnyAuthLoading || isAuthCompleted) return; // Prevent submission if any auth is loading or completed

    setAuthLoading('email');
    setError('');

    try {
      const result = await signIn('email', {
        email: email.trim(),
        redirect: false, // Keep false to handle UI state
        callbackUrl: '/dashboard',
      });

      if (result?.error) {
        setError('Failed to send magic link. Please try again.');
        resetAuthState();
      } else {
        setIsSubmitted(true);
        setAuthCompleted(); // Keep buttons disabled after successful email submission
      }
    } catch (error) {
      console.error('Email sign-in error:', error);
      setError('An unexpected error occurred. Please try again.');
      resetAuthState();
    }
  }, [email, isAnyAuthLoading, setAuthLoading, resetAuthState, isAuthCompleted, setAuthCompleted]);

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (error) setError(''); // Clear error when user starts typing
  };

  const handleTryAgain = () => {
    setIsSubmitted(false);
    setEmail('');
    setError('');
    resetAuthState();
  };

  if (isSubmitted) {
    return (
      <div className="w-full max-w-md">
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
          <div className="flex justify-center mb-4">
            <svg
              className="w-12 h-12 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-green-900 mb-2">
            Check your email
          </h3>
          <p className="text-sm text-green-700 mb-4">
            We&apos;ve sent a magic link to <strong>{email}</strong>.
            Click the link in the email to sign in.
          </p>
          <button
            onClick={handleTryAgain}
            className="text-sm font-medium text-green-600 hover:text-green-500 transition-colors cursor-pointer"
          >
            Try a different email
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="email" className="sr-only">
            Email address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            required
            value={email}
            onChange={handleEmailChange}
            placeholder="Enter your email address"
            className={`w-full px-4 py-3 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${
              error
                ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
            }`}
            disabled={isAnyAuthLoading || isAuthCompleted}
          />
          {error && (
            <p className="mt-2 text-sm text-red-600" role="alert">
              {error}
            </p>
          )}
        </div>

        <button
          type="submit"
          disabled={isAnyAuthLoading || isAuthCompleted || !email.trim()}
          className="w-full flex items-center justify-center gap-3 rounded-lg bg-white px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium text-gray-700 shadow-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
        >
          {(activeProvider === 'email' && showLoading) ? (
            <>
              <svg className="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending magic link...
            </>
          ) : (
            <>
              <EmailIcon size={20} className="text-gray-500" />
              Send Magic Link
            </>
          )}
        </button>
      </form>
    </div>
  );
}
